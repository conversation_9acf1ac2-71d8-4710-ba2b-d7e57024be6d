# =====================================================
# SHARED SUPABASE CONFIGURATION - EXAMPLE FILE
# =====================================================
# ⚠️  SECURITY WARNING: This is an example file with placeholder values
# ⚠️  NEVER put real API keys or credentials in this file
# ⚠️  Real values should only go in .env.local files (which are gitignored)
#
# Copy these values to both applications' .env.local files
# Both patient-facing and admin-facing should use identical values
# Replace all placeholder values with your actual credentials

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =====================================================
# APPLICATION-SPECIFIC CONFIGURATION
# =====================================================
# Add these to each application's .env.local as needed

# Admin Configuration (admin-facing only)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_admin_password

# App Configuration (customize per app)
NEXT_PUBLIC_APP_NAME=Your Dental Practice Name
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Email Configuration (patient-facing only)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_gmail_app_password
SMTP_FROM=<EMAIL>
