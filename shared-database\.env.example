# =====================================================
# SHARED SUPABASE CONFIGURATION
# =====================================================
# Copy these values to both applications' .env.local files
# Both patient-facing and admin-facing should use identical values

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://yuyruqdqlkecsnschziv.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.vCcS1fALVF4yWdykoWNqaLs2UQhjpOaDbA8G4GYJIxc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.__oOcylNRqIPE1nrFPs8353CmqvMLQrKEgULT52aa_U

# =====================================================
# APPLICATION-SPECIFIC CONFIGURATION
# =====================================================
# Add these to each application's .env.local as needed

# Admin Configuration (admin-facing only)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin@1234

# App Configuration (customize per app)
NEXT_PUBLIC_APP_NAME=Dental Admin Dashboard
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Email Configuration (patient-facing only)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>
